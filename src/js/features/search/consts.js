import locationDetector from "components/locationDetector";
import { getCurrentConfig } from "config/domainConfig";
import {
    AI_SUMMARY_SCORES,
    IS_ASK_AVAILABLE_FOR_FILE,
    IS_COPILOT_MULTI_SELECT_POSSIBLE_FOR_FILE,
    VISUAL_INDICATOR_ACTIVE_LINKS
} from "features/visualIndicators/consts";

export default {
    input: {
        DEFAULT_WIDTH_PX: 314,
        DEFAULT_RIGHT_MARGIN_PX: 24,
        DEFAULT_LEFT_MARGIN_PX: 400, // reserved to avoid overlapping with breadcrumbs
        EXPANDED_WIDTH_PX: 500,
        EXPANDED_WIDTH_WITH_SEARCH_HISTORY_PX: 670,
        EXPANDED_LEFT_PX: 52
    }
};

export const METADATA_TAGS_FIELD = "METADATA_TAGS";
export const COLLABORATION_FIELD = "COLLABORATION";

export const getSearchRequestFields = () => {
    const {
        collaborativeEditingEnabled,
        viewerOnlyPrivEnabled,
        watermarkFileDownloadAndPreviewEnabled,
        activeLinksIndicatorEnabled,
        isAskRowActionEnabled,
        isKbaEnabled,
        isGlobalCopilotEnabled,
        showMetadataTagColumnsInListing,
        nlSearchEnabled
    } = getCurrentConfig().features;

    return {
        additionalFields: [
            collaborativeEditingEnabled && "COLLABORATION",
            nlSearchEnabled && AI_SUMMARY_SCORES,
            (viewerOnlyPrivEnabled || watermarkFileDownloadAndPreviewEnabled) && "PREVIEW_RESTRICTIONS",
            activeLinksIndicatorEnabled && VISUAL_INDICATOR_ACTIVE_LINKS,
            isAskRowActionEnabled && IS_ASK_AVAILABLE_FOR_FILE,
            isGlobalCopilotEnabled && IS_COPILOT_MULTI_SELECT_POSSIBLE_FOR_FILE,
            !locationDetector.isDocumentRoomPortalOrClientPortal() &&
                showMetadataTagColumnsInListing &&
                METADATA_TAGS_FIELD
        ].filter(Boolean)
    };
};

export const getBrowseModeRequestFields = () => {
    const {
        showMetadataTagColumnsInListing,
        isGlobalCopilotEnabled,
        fileListingColumnFiltersEnabled,
        isAskRowActionEnabled,
        collaborativeEditingEnabled,
        activeLinksIndicatorEnabled
    } = getCurrentConfig().features;

    const isDocumentRoomPortalOrClientPortal = locationDetector.isDocumentRoomPortalOrClientPortal();
    const showCollaborationField =
        fileListingColumnFiltersEnabled && collaborativeEditingEnabled && !isDocumentRoomPortalOrClientPortal;
    const showAskField =
        fileListingColumnFiltersEnabled && isAskRowActionEnabled && !isDocumentRoomPortalOrClientPortal;
    const showTagsField = showMetadataTagColumnsInListing && !isDocumentRoomPortalOrClientPortal;

    const showActiveLinks =
        fileListingColumnFiltersEnabled && activeLinksIndicatorEnabled && !isDocumentRoomPortalOrClientPortal;

    return {
        additionalFields: [
            showCollaborationField && COLLABORATION_FIELD,
            showTagsField && METADATA_TAGS_FIELD,
            showAskField && IS_ASK_AVAILABLE_FOR_FILE,
            isGlobalCopilotEnabled && IS_COPILOT_MULTI_SELECT_POSSIBLE_FOR_FILE,
            showActiveLinks && VISUAL_INDICATOR_ACTIVE_LINKS
        ].filter(Boolean)
    };
};

export const PRIVATE = "private";

export const MAX_SEARCH_DROPDOWN_ITEMS = 7;
export const MAX_SEARCH_DROPDOWN_BEST_MATCHES_ITEMS = 5;
export const MAX_SEARCH_DROPDOWN_RECENT_SEARCHES_ITEMS = 5;
export const MAX_SEARCH_DROPDOWN_RECENTLY_OPENED_ITEMS = 2;
